console.log('Hello background!', { id: chrome.runtime.id });

// 检查标签页是否为有效的网页
function isValidWebPage(url: string): boolean {
  if (!url) return false;
  return url.startsWith('http://') || url.startsWith('https://');
}

// 设置图标状态
async function setIconState(tabId: number, enabled: boolean) {
  try {
    // 启用或禁用点击功能
    if (enabled) {
      await chrome.action.enable(tabId);
      // 设置图标为正常状态
      await chrome.action.setIcon({
        tabId: tabId,
        path: {
          "16": "assets/icon.png",
          "32": "assets/icon.png",
          "48": "assets/icon.png",
          "128": "assets/icon.png"
        }
      });
    } else {
      await chrome.action.disable(tabId);
      // 禁用状态下，Chrome会自动将图标变灰
    }
  } catch (error) {
    console.error('Error setting icon state:', error);
  }
}

chrome.action.onClicked.addListener(async (tab) => {
  try {
    // 检查是否为有效网页
    if (!isValidWebPage(tab.url)) {
      return;
    }

    // 获取当前选择状态（使用存储而不是badge）
    const result = await chrome.storage.local.get([`selection_state_${tab.id}`]);
    const isSelecting = result[`selection_state_${tab.id}`] || false;
    const nextState = !isSelecting;

    // 保存状态到存储
    await chrome.storage.local.set({ [`selection_state_${tab.id}`]: nextState });

    if (nextState) {
      chrome.tabs.sendMessage(tab.id, { action: 'startElementSelection' });
    } else {
      chrome.tabs.sendMessage(tab.id, { action: 'stopElementSelection' });
    }
  } catch (error) {
    console.error('Error in action click handler:', error);
  }
});

chrome.runtime.onInstalled.addListener(({ reason }) => {
  if (reason === 'install') {
    chrome.storage.local.set({ installDate: Date.now() });
  }

  // 不再设置badge文本
  // chrome.action.setBadgeText({
  //   text: 'OFF',
  // });

  chrome.contextMenus.create({
    id: "css-cliper",
    title: "收藏夹",
    contexts: ["all"]
  });
});

// 监听调试器分离事件
chrome.debugger.onDetach.addListener((source, reason) => {
  console.log('Debugger detached from tab', source.tabId, 'reason:', reason);
});

// 监听调试器事件
chrome.debugger.onEvent.addListener((source, method, params) => {
  // TODO: 处理调试器事件，如 DOM 变化等
  // console.log('Debugger event:', method, params);
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "css-cliper") {
    chrome.tabs.create({
      url: chrome.runtime.getURL("tabs/collection.html")
    })
  }
});

// 监听标签页更新事件
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  // 只在页面完全加载后处理
  if (changeInfo.status === 'complete' && tab.url) {
    const isValid = isValidWebPage(tab.url);
    await setIconState(tabId, isValid);

    // 如果不是有效网页，清除选择状态
    if (!isValid) {
      await chrome.storage.local.remove([`selection_state_${tabId}`]);
    }
  }
});

// 监听标签页激活事件
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    if (tab.url) {
      const isValid = isValidWebPage(tab.url);
      await setIconState(activeInfo.tabId, isValid);
    }
  } catch (error) {
    console.error('Error handling tab activation:', error);
  }
});

// 监听标签页关闭事件，清理存储
chrome.tabs.onRemoved.addListener(async (tabId) => {
  try {
    await chrome.storage.local.remove([`selection_state_${tabId}`]);
  } catch (error) {
    console.error('Error cleaning up tab storage:', error);
  }
});

export {};
