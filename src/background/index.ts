console.log('Hello background!', { id: chrome.runtime.id });

chrome.action.onClicked.addListener(async (tab) => {
  try {
    const prevState = await chrome.action.getBadgeText({ tabId: tab.id });
    const nextState = prevState === 'ON' ? 'OFF' : 'ON';

    await chrome.action.setBadgeText({
      tabId: tab.id,
      text: nextState,
    });

    if (prevState === 'OFF') {
      chrome.tabs.sendMessage(tab.id, { action: 'startElementSelection' });
    } else {
      chrome.tabs.sendMessage(tab.id, { action: 'stopElementSelection' });
    }
  } catch (error) {
    console.error('Error in action click handler:', error);
  }
});

chrome.runtime.onInstalled.addListener(({ reason }) => {
  if (reason === 'install') {
    chrome.storage.local.set({ installDate: Date.now() });
  }

  chrome.action.setBadgeText({
    text: 'OFF',
  });

  chrome.contextMenus.create({
    id: "css-cliper",
    title: "收藏夹",
    contexts: ["all"]
  });
});

// 监听调试器分离事件
chrome.debugger.onDetach.addListener((source, reason) => {
  console.log('Debugger detached from tab', source.tabId, 'reason:', reason);
});

// 监听调试器事件
chrome.debugger.onEvent.addListener((source, method, params) => {
  // TODO: 处理调试器事件，如 DOM 变化等
  // console.log('Debugger event:', method, params);
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "css-cliper") {
    chrome.tabs.create({
      url: chrome.runtime.getURL("tabs/collection.html")
    })
  }
});

const GOOGLE_ORIGIN = 'https://www.google.com';

// chrome.tabs.onUpdated.addListener(async (tabId, info, tab) => {
//   if (!tab.url) return;
//   const url = new URL(tab.url);
//
//   if (url.origin !== GOOGLE_ORIGIN) {
//     await chrome.sidePanel.setOptions({
//       tabId,
//       path: 'sidepanel.html',
//       enabled: true
//     });
//   } else {
//     await chrome.sidePanel.setOptions({
//       tabId,
//       enabled: false
//     });
//   }
// });

export {};
