import type { PlasmoMessaging } from "@plasmohq/messaging"

const handler: PlasmoMessaging.MessageHandler = async (req, res) => {
  try {
    const tabId = req.sender?.tab?.id

    if (!tabId) {
      res.send({ success: false, error: "No tab ID available" })
      return
    }

    // 清除选择状态（不再使用badge）
    await chrome.storage.local.remove([`selection_state_${tabId}`]);

    res.send({ success: true })
  } catch (error: any) {
    console.error("Error in exitElementSelection handler:", error)
    res.send({ success: false, error: error.message })
  }
}

export default handler
