{"name": "crx-css-cliper", "displayName": "css cliper", "version": "0.0.1", "description": "网页 dom+css 抓取插件", "author": "y<PERSON><PERSON>", "private": true, "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@plasmohq/messaging": "^0.6.2", "@plasmohq/storage": "^1.9.0", "codesandbox": "^2.2.3", "dayjs": "^1.11.13", "firebase": "^10.7.1", "html2canvas": "^1.4.1", "plasmo": "^0.90.5", "postcss": "8.4.38", "postcss-selector-parser": "^6.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "specificity": "^1.0.0", "styled-components": "^6.1.9"}, "devDependencies": {"@plasmohq/prettier-plugin-sort-imports": "4.0.1", "@types/chrome": "0.0.268", "@types/html2canvas": "^1.0.0", "@types/node": "20.11.5", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "prettier": "3.2.4", "sass": "^1.75.0", "typescript": "^5.3.3"}, "manifest": {"host_permissions": ["https://*/*"], "permissions": ["activeTab", "storage", "scripting", "contextMenus", "debugger", "identity", "tabs"], "commands": {"_execute_action": {"suggested_key": {"default": "Ctrl+I", "mac": "Command+I"}}}}}